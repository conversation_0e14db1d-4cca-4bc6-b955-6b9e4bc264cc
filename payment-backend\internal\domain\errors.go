package domain

import "errors"

// 支付相关错误定义
var (
	// ErrPaymentNotFound 支付未找到
	ErrPaymentNotFound = errors.New("payment not found")
	
	// ErrSessionNotFound 会话未找到
	ErrSessionNotFound = errors.New("session not found")
	
	// ErrInvalidPaymentID 无效的支付ID
	ErrInvalidPaymentID = errors.New("invalid payment ID")
	
	// ErrInvalidSessionID 无效的会话ID
	ErrInvalidSessionID = errors.New("invalid session ID")
	
	// ErrInvalidProvider 无效的支付提供商
	ErrInvalidProvider = errors.New("invalid payment provider")
	
	// ErrInvalidAmount 无效的金额
	ErrInvalidAmount = errors.New("invalid amount")
	
	// ErrInvalidCurrency 无效的货币
	ErrInvalidCurrency = errors.New("invalid currency")
	
	// ErrSessionExpired 会话已过期
	ErrSessionExpired = errors.New("session expired")
	
	// ErrPaymentAlreadyCompleted 支付已完成
	ErrPaymentAlreadyCompleted = errors.New("payment already completed")
	
	// ErrPaymentAlreadyCancelled 支付已取消
	ErrPaymentAlreadyCancelled = errors.New("payment already cancelled")
	
	// ErrInsufficientFunds 余额不足
	ErrInsufficientFunds = errors.New("insufficient funds")
	
	// ErrInvalidWebhookSignature 无效的Webhook签名
	ErrInvalidWebhookSignature = errors.New("invalid webhook signature")
	
	// ErrWebhookProcessingFailed Webhook处理失败
	ErrWebhookProcessingFailed = errors.New("webhook processing failed")
	
	// ErrGatewayUnavailable 支付网关不可用
	ErrGatewayUnavailable = errors.New("payment gateway unavailable")
	
	// ErrGatewayTimeout 支付网关超时
	ErrGatewayTimeout = errors.New("payment gateway timeout")
	
	// ErrRefundFailed 退款失败
	ErrRefundFailed = errors.New("refund failed")
	
	// ErrRefundNotAllowed 不允许退款
	ErrRefundNotAllowed = errors.New("refund not allowed")
	
	// ErrDuplicatePayment 重复支付
	ErrDuplicatePayment = errors.New("duplicate payment")
	
	// ErrDuplicateSession 重复会话
	ErrDuplicateSession = errors.New("duplicate session")
)

// PaymentError 支付错误类型
type PaymentError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *PaymentError) Error() string {
	if e.Details != "" {
		return e.Message + ": " + e.Details
	}
	return e.Message
}

// NewPaymentError 创建支付错误
func NewPaymentError(code, message, details string) *PaymentError {
	return &PaymentError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// 常用错误代码
const (
	ErrCodeInvalidRequest      = "INVALID_REQUEST"
	ErrCodePaymentNotFound     = "PAYMENT_NOT_FOUND"
	ErrCodeSessionNotFound     = "SESSION_NOT_FOUND"
	ErrCodeInvalidProvider     = "INVALID_PROVIDER"
	ErrCodeSessionExpired      = "SESSION_EXPIRED"
	ErrCodePaymentCompleted    = "PAYMENT_COMPLETED"
	ErrCodePaymentCancelled    = "PAYMENT_CANCELLED"
	ErrCodeInsufficientFunds   = "INSUFFICIENT_FUNDS"
	ErrCodeInvalidSignature    = "INVALID_SIGNATURE"
	ErrCodeWebhookFailed       = "WEBHOOK_FAILED"
	ErrCodeGatewayUnavailable  = "GATEWAY_UNAVAILABLE"
	ErrCodeGatewayTimeout      = "GATEWAY_TIMEOUT"
	ErrCodeRefundFailed        = "REFUND_FAILED"
	ErrCodeRefundNotAllowed    = "REFUND_NOT_ALLOWED"
	ErrCodeDuplicatePayment    = "DUPLICATE_PAYMENT"
	ErrCodeDuplicateSession    = "DUPLICATE_SESSION"
	ErrCodeInternalError       = "INTERNAL_ERROR"
)
