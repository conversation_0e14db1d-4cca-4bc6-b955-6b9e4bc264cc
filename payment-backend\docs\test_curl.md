# API 测试 curl 命令集合

本文档包含了支付后端服务的各种 API 接口的 curl 测试命令，便于开发和测试使用。

## 环境变量设置

### Linux/macOS (Bash/Zsh)

```bash
# 设置服务器地址（可选，默认为 localhost:8080）
export SERVER_HOST=http://localhost:8080

# 或者设置为其他地址
# export SERVER_HOST=http://*************:8080
# export SERVER_HOST=https://api.example.com
```

### Windows PowerShell

```powershell
# 设置服务器地址（可选，默认为 localhost:8080）
$env:SERVER_HOST = "http://localhost:8080"

# 或者设置为其他地址
# $env:SERVER_HOST = "http://*************:8080"
# $env:SERVER_HOST = "https://api.example.com"
```

## 订单管理接口

### 创建订单 (POST /orders)

**Stripe 支付 - Linux/macOS:**
```bash
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_stripe_001",
    "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
```

**Stripe 支付 - Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }
curl -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user123" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_stripe_001",
    "product_desc": "Stripe Premium Plan",
    "price_id": "price_stripe_monthly",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'

```

**预期响应**

```json
{
	"order_id": "20250711145912STRIPE1943565722550534144",
	"checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1auhmrzLokvHFHOHdEJVvyHTARBC9iuyLLfOpLIF70MsRfv9VPtS3DPmc#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl",
	"amount": 0,
	"currency": "",
	"expires_at": "2025-07-12T14:59:13.4290951+08:00"
}
```

### 支付

**PayPal 支付 - Linux/macOS:**
```bash
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user456" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_paypal_001",
    "product_desc": "PayPal Premium Plan",
    "price_id": "price_paypal_monthly",
    "quantity": 2,
    "currency": "EUR",
    "payed_method": "paypal",
    "psp_provider": "paypal"
  }'
```

**PayPal 支付 - Windows PowerShell:**

```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }
curl -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user456" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_paypal_001",
    "product_desc": "PayPal Premium Plan",
    "price_id": "price_paypal_monthly",
    "quantity": 2,
    "currency": "EUR",
    "payed_method": "paypal",
    "psp_provider": "paypal"
  }'
```


#### 检查数据库

```bash
docker exec -it mysql-dev bash
mysql -uroot -p123456
use aibook_payment;
show tables;
```

### 2. 获取订单详情 (GET /orders/:order_id)

**Linux/macOS:**
```bash
# 使用订单ID获取订单详情
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789" \
  -H "x-user-id: user123" \
  -H "x-role: customer"
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }
curl -X GET "$serverHost/api/v1/order-service/orders/20250710153045999stripe**********123456789" `
  -H "x-user-id: user123" `
  -H "x-role: customer"
```

### 3. 根据数据库ID获取订单 (GET /orders/id/:id)

**Linux/macOS:**
```bash
# 使用数据库ID获取订单详情
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/id/1" \
  -H "x-user-id: user123" \
  -H "x-role: customer"
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }
curl -X GET "$serverHost/api/v1/order-service/orders/id/1" `
  -H "x-user-id: user123" `
  -H "x-role: customer"
```

### 4. 获取用户订单列表 (GET /orders)

**Linux/macOS:**
```bash
# 获取用户订单列表（默认分页）
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "x-user-id: user123" \
  -H "x-role: customer"

# 带分页参数
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders?limit=10&offset=0" \
  -H "x-user-id: user123" \
  -H "x-role: customer"
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# 获取用户订单列表（默认分页）
curl -X GET "$serverHost/api/v1/order-service/orders" `
  -H "x-user-id: user123" `
  -H "x-role: customer"

# 带分页参数
curl -X GET "$serverHost/api/v1/order-service/orders?limit=10&offset=0" `
  -H "x-user-id: user123" `
  -H "x-role: customer"
```

### 5. 更新订单 (PUT /orders/:order_id)

**Linux/macOS:**
```bash
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "status": "completed",
    "notes": "Order completed successfully"
  }'
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }
curl -X PUT "$serverHost/api/v1/order-service/orders/20250710153045999stripe**********123456789" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user123" `
  -H "x-role: customer" `
  -d '{
    "status": "completed",
    "notes": "Order completed successfully"
  }'
```

### 6. 取消订单 (POST /orders/:order_id/cancel)

**Linux/macOS:**
```bash
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789/cancel" \
  -H "x-user-id: user123" \
  -H "x-role: customer"
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }
curl -X POST "$serverHost/api/v1/order-service/orders/20250710153045999stripe**********123456789/cancel" `
  -H "x-user-id: user123" `
  -H "x-role: customer"
```

### 7. 退款订单 (POST /orders/:order_id/refund)

**Linux/macOS:**
```bash
# 全额退款
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789/refund" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{}'

# 部分退款
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789/refund" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "amount": 50.00,
    "reason": "Partial refund requested by customer"
  }'
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# 全额退款
curl -X POST "$serverHost/api/v1/order-service/orders/20250710153045999stripe**********123456789/refund" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user123" `
  -H "x-role: customer" `
  -d '{}'

# 部分退款
curl -X POST "$serverHost/api/v1/order-service/orders/20250710153045999stripe**********123456789/refund" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user123" `
  -H "x-role: customer" `
  -d '{
    "amount": 50.00,
    "reason": "Partial refund requested by customer"
  }'
```

## Webhook 接口

### 处理支付提供商 Webhook (POST /webhooks/:provider)

**Linux/macOS:**
```bash
# Stripe Webhook
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/webhooks/stripe" \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: t=**********,v1=signature_here" \
  -d '{
    "id": "evt_test_webhook",
    "object": "event",
    "type": "checkout.session.completed",
    "data": {
      "object": {
        "id": "cs_test_session_id",
        "payment_status": "paid"
      }
    }
  }'

# PayPal Webhook
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/webhooks/paypal" \
  -H "Content-Type: application/json" \
  -H "PayPal-Transmission-Id: webhook_transmission_id" \
  -d '{
    "event_type": "PAYMENT.CAPTURE.COMPLETED",
    "resource": {
      "id": "payment_id_here",
      "status": "COMPLETED"
    }
  }'
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# Stripe Webhook
curl -X POST "$serverHost/api/v1/order-service/webhooks/stripe" `
  -H "Content-Type: application/json" `
  -H "Stripe-Signature: t=**********,v1=signature_here" `
  -d '{
    "id": "evt_test_webhook",
    "object": "event",
    "type": "checkout.session.completed",
    "data": {
      "object": {
        "id": "cs_test_session_id",
        "payment_status": "paid"
      }
    }
  }'

# PayPal Webhook
curl -X POST "$serverHost/api/v1/order-service/webhooks/paypal" `
  -H "Content-Type: application/json" `
  -H "PayPal-Transmission-Id: webhook_transmission_id" `
  -d '{
    "event_type": "PAYMENT.CAPTURE.COMPLETED",
    "resource": {
      "id": "payment_id_here",
      "status": "COMPLETED"
    }
  }'
```

## 错误测试用例

### 1. 缺少认证头部

**Linux/macOS:**
```bash
# 缺少 x-user-id
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "stripe"
  }'

# 预期响应: 401 Unauthorized
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# 缺少 x-user-id
curl -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "stripe"
  }'

# 预期响应: 401 Unauthorized
```

### 2. 无效的请求数据

**Linux/macOS:**
```bash
# 缺少必需字段
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_123"
  }'

# 预期响应: 400 Bad Request
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# 缺少必需字段
curl -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user123" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_123"
  }'

# 预期响应: 400 Bad Request
```

### 3. 不支持的支付提供商

**Linux/macOS:**
```bash
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "unsupported_provider"
  }'

# 预期响应: 500 Internal Server Error
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

curl -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: user123" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "unsupported_provider"
  }'

# 预期响应: 500 Internal Server Error
```

## 测试脚本

### 快速测试脚本

**Linux/macOS - 创建 `test_orders.sh`：**

```bash
#!/bin/bash

# 设置服务器地址
SERVER_HOST=${SERVER_HOST:-http://localhost:8080}

echo "Testing Order API..."
echo "Server: $SERVER_HOST"
echo

# 测试创建订单
echo "1. Creating order..."
ORDER_RESPONSE=$(curl -s -X POST "$SERVER_HOST/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: test_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_test",
    "product_desc": "Test Product",
    "price_id": "price_test",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }')

echo "Response: $ORDER_RESPONSE"
echo

# 提取订单ID（需要 jq 工具）
if command -v jq &> /dev/null; then
    ORDER_ID=$(echo "$ORDER_RESPONSE" | jq -r '.order_id')
    echo "Order ID: $ORDER_ID"

    if [ "$ORDER_ID" != "null" ] && [ "$ORDER_ID" != "" ]; then
        echo "2. Getting order details..."
        curl -s -X GET "$SERVER_HOST/api/v1/order-service/orders/$ORDER_ID" \
          -H "x-user-id: test_user" \
          -H "x-role: customer" | jq '.'
    fi
fi
```

**Windows PowerShell - 创建 `test_orders.ps1`：**

```powershell
# 设置服务器地址
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

Write-Host "Testing Order API..."
Write-Host "Server: $serverHost"
Write-Host

# 测试创建订单
Write-Host "1. Creating order..."
$orderResponse = curl -s -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: test_user" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_test",
    "product_desc": "Test Product",
    "price_id": "price_test",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'

Write-Host "Response: $orderResponse"
Write-Host

# 提取订单ID（需要安装 jq 或使用 PowerShell 的 ConvertFrom-Json）
try {
    $responseObj = $orderResponse | ConvertFrom-Json
    $orderId = $responseObj.order_id
    Write-Host "Order ID: $orderId"

    if ($orderId -and $orderId -ne "null") {
        Write-Host "2. Getting order details..."
        $orderDetails = curl -s -X GET "$serverHost/api/v1/order-service/orders/$orderId" `
          -H "x-user-id: test_user" `
          -H "x-role: customer"

        $orderDetails | ConvertFrom-Json | ConvertTo-Json -Depth 10
    }
} catch {
    Write-Host "Failed to parse JSON response: $_"
}
```

## 注意事项

### 通用注意事项

1. **认证头部**: 所有需要认证的接口都必须包含 `x-user-id` 和 `x-role` 头部
2. **环境变量**: 使用 `SERVER_HOST` 环境变量可以灵活切换测试环境
3. **数据格式**: 请求体必须是有效的 JSON 格式
4. **错误处理**: 注意检查响应状态码和错误信息
5. **订单ID**: 创建订单后返回的 `order_id` 用于后续操作

### Windows PowerShell 特殊注意事项

1. **curl 安装**: 确保已安装 curl for Windows
   ```powershell
   # 检查 curl 是否可用
   curl --version

   # 如果没有安装，可以通过以下方式安装：
   # 1. 使用 Chocolatey: choco install curl
   # 2. 使用 Scoop: scoop install curl
   # 3. 从官网下载: https://curl.se/windows/
   ```

2. **行继续符**: PowerShell 使用反引号 `` ` `` 而不是反斜杠 `\`

3. **环境变量**: 使用 `$env:VARIABLE_NAME` 语法

4. **JSON 解析**: 可以使用 PowerShell 内置的 `ConvertFrom-Json` 和 `ConvertTo-Json`

5. **引号处理**: 在 PowerShell 中，JSON 字符串需要特别注意引号的转义

### 替代方案 - 使用 Invoke-RestMethod (PowerShell 原生)

如果遇到 curl 问题，可以使用 PowerShell 的原生 HTTP 客户端：

```powershell
# 创建订单 - PowerShell 原生方式
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

$headers = @{
    "Content-Type" = "application/json"
    "x-user-id" = "user123"
    "x-role" = "customer"
}

$body = @{
    product_id = "prod_123"
    product_desc = "Premium Subscription"
    price_id = "price_456"
    quantity = 1
    currency = "USD"
    payed_method = "stripe"
    psp_provider = "stripe"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$serverHost/api/v1/order-service/orders" `
                                  -Method POST `
                                  -Headers $headers `
                                  -Body $body

    Write-Host "Order created successfully:"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}
```

## 常用测试数据

```json
{
  "test_users": [
    {"user_id": "user123", "role": "customer"},
    {"user_id": "admin001", "role": "admin"},
    {"user_id": "test_user", "role": "customer"}
  ],
  "test_products": [
    {"product_id": "prod_123", "price_id": "price_456"},
    {"product_id": "prod_premium", "price_id": "price_premium_monthly"},
    {"product_id": "prod_basic", "price_id": "price_basic_yearly"}
  ],
  "supported_providers": ["stripe", "paypal"],
  "supported_currencies": ["USD", "EUR", "GBP", "JPY"]
}
```

## 管理员接口测试

### 管理员权限测试

```bash
# 管理员用户访问（需要 admin 或 super_admin 角色）
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/admin/orders" \
  -H "x-user-id: admin001" \
  -H "x-role: admin"

# 普通用户访问管理员接口（应该返回 403）
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/admin/orders" \
  -H "x-user-id: user123" \
  -H "x-role: customer"
```

## 性能测试

### 并发创建订单测试

```bash
# 使用 GNU parallel 进行并发测试（需要安装 parallel）
seq 1 10 | parallel -j 5 curl -s -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user{}" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_load_test",
    "product_desc": "Load Test Product {}",
    "price_id": "price_load_test",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

### 压力测试脚本

```bash
#!/bin/bash
# stress_test.sh

SERVER_HOST=${SERVER_HOST:-http://localhost:8080}
CONCURRENT_USERS=10
REQUESTS_PER_USER=5

echo "Starting stress test..."
echo "Server: $SERVER_HOST"
echo "Concurrent users: $CONCURRENT_USERS"
echo "Requests per user: $REQUESTS_PER_USER"

for i in $(seq 1 $CONCURRENT_USERS); do
  {
    for j in $(seq 1 $REQUESTS_PER_USER); do
      curl -s -w "%{http_code} %{time_total}s\n" \
        -X POST "$SERVER_HOST/api/v1/order-service/orders" \
        -H "Content-Type: application/json" \
        -H "x-user-id: stress_user_$i" \
        -H "x-role: customer" \
        -d "{
          \"product_id\": \"prod_stress_$i\",
          \"product_desc\": \"Stress Test Product $i-$j\",
          \"price_id\": \"price_stress\",
          \"quantity\": 1,
          \"currency\": \"USD\",
          \"payed_method\": \"stripe\",
          \"psp_provider\": \"stripe\"
        }" > /dev/null
      echo "User $i Request $j completed"
    done
  } &
done

wait
echo "Stress test completed"
```

## 环境切换示例

### 开发环境

**Linux/macOS:**
```bash
export SERVER_HOST=http://localhost:8080
export TEST_USER_ID=dev_user
export TEST_ROLE=customer

# 测试创建订单
curl -X POST "$SERVER_HOST/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: $TEST_USER_ID" \
  -H "x-role: $TEST_ROLE" \
  -d '{
    "product_id": "dev_prod_001",
    "product_desc": "Development Test Product",
    "price_id": "dev_price_001",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

**Windows PowerShell:**
```powershell
$env:SERVER_HOST = "http://localhost:8080"
$env:TEST_USER_ID = "dev_user"
$env:TEST_ROLE = "customer"

# 测试创建订单
curl -X POST "$env:SERVER_HOST/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: $env:TEST_USER_ID" `
  -H "x-role: $env:TEST_ROLE" `
  -d '{
    "product_id": "dev_prod_001",
    "product_desc": "Development Test Product",
    "price_id": "dev_price_001",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

### 测试环境

**Linux/macOS:**
```bash
export SERVER_HOST=http://test-api.example.com
export TEST_USER_ID=test_user
export TEST_ROLE=customer

# 使用相同的命令，但指向测试环境
curl -X POST "$SERVER_HOST/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: $TEST_USER_ID" \
  -H "x-role: $TEST_ROLE" \
  -d '{
    "product_id": "test_prod_001",
    "product_desc": "Test Environment Product",
    "price_id": "test_price_001",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

**Windows PowerShell:**
```powershell
$env:SERVER_HOST = "http://test-api.example.com"
$env:TEST_USER_ID = "test_user"
$env:TEST_ROLE = "customer"

# 使用相同的命令，但指向测试环境
curl -X POST "$env:SERVER_HOST/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: $env:TEST_USER_ID" `
  -H "x-role: $env:TEST_ROLE" `
  -d '{
    "product_id": "test_prod_001",
    "product_desc": "Test Environment Product",
    "price_id": "test_price_001",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

### 生产环境

**Linux/macOS:**
```bash
export SERVER_HOST=https://api.production.com
export TEST_USER_ID=prod_test_user
export TEST_ROLE=customer

# 生产环境测试（谨慎使用）
curl -X POST "$SERVER_HOST/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: $TEST_USER_ID" \
  -H "x-role: $TEST_ROLE" \
  -d '{
    "product_id": "prod_real_001",
    "product_desc": "Production Test Product",
    "price_id": "prod_price_001",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

**Windows PowerShell:**
```powershell
$env:SERVER_HOST = "https://api.production.com"
$env:TEST_USER_ID = "prod_test_user"
$env:TEST_ROLE = "customer"

# 生产环境测试（谨慎使用）
curl -X POST "$env:SERVER_HOST/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: $env:TEST_USER_ID" `
  -H "x-role: $env:TEST_ROLE" `
  -d '{
    "product_id": "prod_real_001",
    "product_desc": "Production Test Product",
    "price_id": "prod_price_001",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

## 调试技巧

### 1. 详细输出

```bash
# 显示详细的请求和响应信息
curl -v -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: debug_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "debug_prod",
    "price_id": "debug_price",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
```

### 2. 保存响应到文件

```bash
# 保存响应头和响应体
curl -D headers.txt -o response.json \
  -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: file_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "file_prod",
    "price_id": "file_price",
    "quantity": 1,
    "psp_provider": "stripe"
  }'

# 查看保存的文件
cat headers.txt
cat response.json | jq '.'
```

### 3. 时间测量

```bash
# 测量请求时间
curl -w "Total time: %{time_total}s\nHTTP code: %{http_code}\n" \
  -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: timing_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "timing_prod",
    "price_id": "timing_price",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
```

## 自动化测试集成

### CI/CD 管道中的 API 测试

```bash
#!/bin/bash
# ci_api_test.sh

set -e  # 遇到错误立即退出

SERVER_HOST=${SERVER_HOST:-http://localhost:8080}
TEST_USER_ID=${TEST_USER_ID:-ci_test_user}

echo "Running CI API tests..."

# 健康检查
echo "1. Health check..."
curl -f "$SERVER_HOST/health" || exit 1

# 创建订单测试
echo "2. Create order test..."
RESPONSE=$(curl -s -X POST "$SERVER_HOST/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: $TEST_USER_ID" \
  -H "x-role: customer" \
  -d '{
    "product_id": "ci_prod",
    "price_id": "ci_price",
    "quantity": 1,
    "psp_provider": "stripe"
  }')

# 检查响应是否包含 order_id
if echo "$RESPONSE" | grep -q "order_id"; then
  echo "✓ Create order test passed"
else
  echo "✗ Create order test failed"
  echo "Response: $RESPONSE"
  exit 1
fi

echo "All CI API tests passed!"
```

这个文档现在包含了：

1. **基础 curl 命令** - 针对所有订单管理接口
2. **环境变量支持** - 使用 `SERVER_HOST` 灵活切换环境
3. **错误测试用例** - 验证错误处理
4. **测试脚本** - 自动化测试工具
5. **性能测试** - 并发和压力测试
6. **调试技巧** - 详细输出、文件保存、时间测量
7. **CI/CD 集成** - 自动化测试脚本

您现在可以使用这个文档进行各种 API 测试，包括：
- 手动测试单个接口
- 批量测试多个场景
- 性能和压力测试
- 自动化集成测试

文档已保存在 `payment-backend/docs/test_curl.md`，方便您后续参考和使用。
