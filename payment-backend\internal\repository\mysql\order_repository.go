package mysql

import (
	"fmt"

	"gorm.io/gorm"

	"payment-backend/internal/db"
	"payment-backend/internal/domain"
)

// orderRepository MySQL订单仓储实现
type orderRepository struct {
	db *gorm.DB
}

// NewOrderRepository 创建MySQL订单仓储
func NewOrderRepository(database *gorm.DB) domain.OrderRepository {
	return &orderRepository{
		db: database,
	}
}

// Create 创建订单记录
func (r *orderRepository) Create(order *domain.Order) error {
	model := &db.OrderModel{}
	model.FromDomain(order)

	if err := r.db.Create(model).Error; err != nil {
		return fmt.Errorf("failed to create order: %w", err)
	}

	// 更新domain对象的ID
	order.ID = model.ID
	order.OrderID = model.OrderID
	order.CreatedAt = model.CreatedAt
	order.UpdatedAt = model.UpdatedAt

	return nil
}

// GetByID 根据ID获取订单记录
func (r *orderRepository) GetByID(id uint64) (*domain.Order, error) {
	var model db.OrderModel

	if err := r.db.Where("id = ? AND deleted = 0", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get order by ID: %w", err)
	}

	return model.ToDomain(), nil
}

// GetByOrderID 根据订单ID获取订单记录
func (r *orderRepository) GetByOrderID(orderID string) (*domain.Order, error) {
	var model db.OrderModel

	if err := r.db.Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with order ID %s not found", orderID)
		}
		return nil, fmt.Errorf("failed to get order by order ID: %w", err)
	}

	return model.ToDomain(), nil
}

// GetByUserID 根据用户ID获取订单列表
func (r *orderRepository) GetByUserID(userID string, limit, offset int) ([]*domain.Order, error) {
	var models []db.OrderModel

	query := r.db.Where("user_id = ? AND deleted = 0", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get orders by user ID: %w", err)
	}

	// 转换为domain模型
	orders := make([]*domain.Order, len(models))
	for i, model := range models {
		orders[i] = model.ToDomain()
	}

	return orders, nil
}

// GetByPSPPaymentID 根据PSP支付ID获取订单记录
func (r *orderRepository) GetByPSPPaymentID(pspPaymentID string) (*domain.Order, error) {
	var model db.OrderModel

	if err := r.db.Where("psp_payment_id = ? AND deleted = 0", pspPaymentID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with PSP payment ID %s not found", pspPaymentID)
		}
		return nil, fmt.Errorf("failed to get order by PSP payment ID: %w", err)
	}

	return model.ToDomain(), nil
}

// Update 更新订单记录
func (r *orderRepository) Update(order *domain.Order) error {
	model := &db.OrderModel{}
	model.FromDomain(order)

	// 使用Select来指定要更新的字段，避免零值问题
	result := r.db.Model(&db.OrderModel{}).
		Where("id = ? AND deleted = 0", order.ID).
		Select("user_id", "product_id", "product_desc", "price_id", "quantity",
			"amount", "net_amount", "currency", "pay_status", "payed_method", "psp_provider",
			"card_number", "payed_at", "refund_status", "refunded_at",
			"psp_product_id", "psp_product_desc", "psp_price_id", "psp_payment_id",
			"psp_customer_id", "psp_customer_email", "psp_subscription_id", "updated_at").
		Updates(model)

	if result.Error != nil {
		return fmt.Errorf("failed to update order: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with ID %d not found or already deleted", order.ID)
	}

	return nil
}

// UpdateStatus 更新订单支付状态
func (r *orderRepository) UpdateStatus(orderID string, payStatus string) error {
	result := r.db.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"pay_status": payStatus,
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update order status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// SoftDelete 软删除订单
func (r *orderRepository) SoftDelete(orderID string) error {
	result := r.db.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"deleted":    1,
			"deleted_at": gorm.Expr("NOW()"),
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete order: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// List 获取订单列表
func (r *orderRepository) List(limit, offset int) ([]*domain.Order, error) {
	var models []db.OrderModel

	query := r.db.Where("deleted = 0").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to list orders: %w", err)
	}

	// 转换为domain模型
	orders := make([]*domain.Order, len(models))
	for i, model := range models {
		orders[i] = model.ToDomain()
	}

	return orders, nil
}

// Count 统计订单总数
func (r *orderRepository) Count() (int64, error) {
	var count int64

	if err := r.db.Model(&db.OrderModel{}).Where("deleted = 0").Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count orders: %w", err)
	}

	return count, nil
}

// CountByUserID 统计用户的订单数量
func (r *orderRepository) CountByUserID(userID string) (int64, error) {
	var count int64

	if err := r.db.Model(&db.OrderModel{}).
		Where("user_id = ? AND deleted = 0", userID).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count orders by user ID: %w", err)
	}

	return count, nil
}

// CreateWithTransaction 在事务中创建订单记录
func (r *orderRepository) CreateWithTransaction(tx any, order *domain.Order) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	model := &db.OrderModel{}
	model.FromDomain(order)

	if err := gormTx.Create(model).Error; err != nil {
		return fmt.Errorf("failed to create order in transaction: %w", err)
	}

	// 更新domain对象的ID
	order.ID = model.ID
	order.OrderID = model.OrderID
	order.CreatedAt = model.CreatedAt
	order.UpdatedAt = model.UpdatedAt

	return nil
}

// UpdateWithTransaction 在事务中更新订单记录
func (r *orderRepository) UpdateWithTransaction(tx any, order *domain.Order) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	model := &db.OrderModel{}
	model.FromDomain(order)

	result := gormTx.Model(&db.OrderModel{}).
		Where("id = ? AND deleted = 0", order.ID).
		Select("user_id", "product_id", "product_desc", "price_id", "quantity",
			"amount", "net_amount", "currency", "pay_status", "payed_method", "psp_provider",
			"card_number", "payed_at", "refund_status", "refunded_at",
			"psp_product_id", "psp_product_desc", "psp_price_id", "psp_payment_id",
			"psp_customer_id", "psp_customer_email", "psp_subscription_id", "updated_at").
		Updates(model)

	if result.Error != nil {
		return fmt.Errorf("failed to update order in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with ID %d not found or already deleted", order.ID)
	}

	return nil
}

// GetByOrderIDWithTransaction 在事务中根据订单ID获取订单记录
func (r *orderRepository) GetByOrderIDWithTransaction(tx any, orderID string) (*domain.Order, error) {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return nil, fmt.Errorf("invalid transaction type")
	}

	var model db.OrderModel

	if err := gormTx.Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with order ID %s not found", orderID)
		}
		return nil, fmt.Errorf("failed to get order by order ID in transaction: %w", err)
	}

	return model.ToDomain(), nil
}

// GetByIDWithTransaction 在事务中根据ID获取订单记录
func (r *orderRepository) GetByIDWithTransaction(tx any, id uint64) (*domain.Order, error) {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return nil, fmt.Errorf("invalid transaction type")
	}

	var model db.OrderModel

	if err := gormTx.Where("id = ? AND deleted = 0", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get order by ID in transaction: %w", err)
	}

	return model.ToDomain(), nil
}

// UpdateStatusWithTransaction 在事务中更新订单支付状态
func (r *orderRepository) UpdateStatusWithTransaction(tx any, orderID string, payStatus string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"pay_status": payStatus,
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update order status in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// SoftDeleteWithTransaction 在事务中软删除订单记录
func (r *orderRepository) SoftDeleteWithTransaction(tx any, orderID string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"deleted":    1,
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete order in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}
