package middleware

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"payment-backend/internal/logger"
)

// UserContext 用户上下文信息
type UserContext struct {
	UserID string `json:"user_id"`
	Role   string `json:"role"`
}

// ContextKey 上下文键类型
type ContextKey string

const (
	// UserContextKey 用户上下文键
	UserContextKey ContextKey = "user_context"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.GetHeader("x-user-id")
		role := c.GetHeader("x-role")

		// 验证必需的头部信息
		if userID == "" {
			log.Warn("Missing x-user-id header",
				logger.String("path", c.Request.URL.Path),
				logger.String("method", c.Request.Method),
			)
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error":   "unauthorized",
				"message": "Missing user ID in request headers",
			})
			c.Abort()
			return
		}

		if role == "" {
			log.Warn("Missing x-role header",
				logger.String("path", c.Request.URL.Path),
				logger.String("method", c.Request.Method),
				logger.String("user_id", userID),
			)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "unauthorized",
				"message": "Missing role in request headers",
			})
			c.Abort()
			return
		}

		// 创建用户上下文
		userCtx := &UserContext{
			UserID: userID,
			Role:   role,
		}

		// 将用户上下文存储到 Gin 上下文中
		c.Set(string(UserContextKey), userCtx)

		// 记录用户信息（用于审计）
		log.Debug("User context extracted",
			logger.String("user_id", userID),
			logger.String("role", role),
			logger.String("path", c.Request.URL.Path),
			logger.String("method", c.Request.Method),
		)

		c.Next()
	}
}

// GetUserContext 从 Gin 上下文中获取用户上下文
func GetUserContext(c *gin.Context) (*UserContext, bool) {
	value, exists := c.Get(string(UserContextKey))
	if !exists {
		return nil, false
	}

	userCtx, ok := value.(*UserContext)
	return userCtx, ok
}

// MustGetUserContext 从 Gin 上下文中获取用户上下文（必须存在）
func MustGetUserContext(c *gin.Context) (*UserContext, error) {
	userCtx, exists := GetUserContext(c)
	if !exists {
		return nil, errors.New("user context not found in gin context")
	}
	return userCtx, nil
}

// OptionalAuthMiddleware 可选认证中间件（不强制要求认证）
func OptionalAuthMiddleware(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.GetHeader("x-user-id")
		role := c.GetHeader("x-role")

		// 如果有用户信息，则设置上下文
		if userID != "" && role != "" {
			userCtx := &UserContext{
				UserID: userID,
				Role:   role,
			}
			c.Set(string(UserContextKey), userCtx)

			log.Debug("Optional user context extracted",
				logger.String("user_id", userID),
				logger.String("role", role),
				logger.String("path", c.Request.URL.Path),
			)
		}

		c.Next()
	}
}

// RequireRole 角色验证中间件
func RequireRole(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userCtx, exists := GetUserContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "unauthorized",
				"message": "User context not found",
			})
			c.Abort()
			return
		}

		// 检查用户角色是否在允许的角色列表中
		roleAllowed := false
		for _, allowedRole := range allowedRoles {
			if userCtx.Role == allowedRole {
				roleAllowed = true
				break
			}
		}

		if !roleAllowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "forbidden",
				"message": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
