# 支付后端服务配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30
  write_timeout: 30
  mode: "release" # debug, release, test

# 数据库配置
database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "payment_user"
  password: "payment_password"
  database: "payment_db"
  ssl_mode: "disable"

# 支付配置
payment:
  providers:
    paypal:
      enabled: true
      api_key: "your_paypal_api_key"
      secret_key: "your_paypal_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/paypal"
        secret: "your_paypal_webhook_secret"
      settings:
        environment: "sandbox" # sandbox, live

    stripe:
      enabled: true
      api_key: "your_stripe_api_key"
      secret_key: "your_stripe_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/stripe"
        secret: "your_stripe_webhook_secret"
      settings:
        environment: "test" # test, live

# 日志配置
log:
  level: "info" # debug, info, warn, error
  format: "json" # json, console
  output: "stdout" # stdout, file
  filename: "logs/payment-backend.log"
  max_size: 100 # MB
  max_backups: 3
  max_age: 28 # days
